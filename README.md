# 微信小程序 - 用户资料页面

## 项目描述
这是一个基于微信小程序的用户资料页面，专为iPhone 16尺寸优化设计。

## 设计特点
- ✅ 使用Flexbox布局，避免绝对定位
- ✅ iPhone 16尺寸适配 (393px宽度)
- ✅ 响应式设计
- ✅ 圆角卡片设计 (16rpx圆角)
- ✅ 轻微阴影效果
- ✅ 微交互动画效果
- ✅ 符合微信小程序设计规范

## 页面功能
- 头像显示
- 昵称展示（可点击编辑）
- 手机号码显示（可点击编辑）
- 邀请码展示
- 推广授权状态
- 用户协议链接
- 个人隐私协议链接

## 技术栈
- 微信小程序原生开发
- Flexbox布局
- WXSS样式
- JavaScript逻辑处理

## 运行方式
1. 使用微信开发者工具打开项目
2. 确保已配置小程序AppID
3. 点击预览或真机调试

## 文件结构
```
├── app.json          # 小程序配置文件
├── app.js            # 小程序逻辑文件
├── app.wxss          # 小程序公共样式文件
├── sitemap.json      # 站点地图
├── pages/
│   └── profile/      # 用户资料页面
│       ├── profile.wxml
│       ├── profile.wxss
│       ├── profile.js
│       └── profile.json
└── images/           # 图片资源目录
    └── README.md
```

## 注意事项
- 请在images目录中添加default-avatar.png头像图片
- 实际项目中需要连接真实的用户数据API
- 需要实现真实的页面跳转逻辑
