// pages/profile/profile.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    avatarUrl: '/images/default-avatar.png', // 默认头像，实际项目中应该从用户数据获取
    nickname: '推客123456>',
    phoneNumber: '159***6790',
    inviteCode: '6586878',
    authStatus: '已授权'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 页面加载时可以从服务器获取用户信息
    this.getUserInfo();
  },

  /**
   * 获取用户信息
   */
  getUserInfo: function() {
    // 这里应该调用API获取用户信息
    // 示例数据已在data中设置
    console.log('获取用户信息');
  },

  /**
   * 点击用户协议
   */
  onUserAgreementTap: function() {
    wx.showToast({
      title: '跳转到用户协议页面',
      icon: 'none'
    });
    // 实际项目中应该跳转到用户协议页面
    // wx.navigateTo({
    //   url: '/pages/agreement/user-agreement'
    // });
  },

  /**
   * 点击个人隐私协议
   */
  onPrivacyAgreementTap: function() {
    wx.showToast({
      title: '跳转到隐私协议页面',
      icon: 'none'
    });
    // 实际项目中应该跳转到隐私协议页面
    // wx.navigateTo({
    //   url: '/pages/agreement/privacy-agreement'
    // });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    // 下拉刷新时重新获取用户信息
    this.getUserInfo();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '用户资料',
      path: '/pages/profile/profile'
    };
  }
});
