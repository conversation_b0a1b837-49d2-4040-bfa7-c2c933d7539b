<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 头像区域 -->
  <view class="avatar-section">
    <view class="avatar-row">
      <text class="label">头像</text>
      <view class="avatar-container">
        <image class="avatar" src="{{avatarUrl}}" mode="aspectFill"></image>
      </view>
    </view>
  </view>

  <!-- 昵称区域 -->
  <view class="info-section">
    <view class="info-row">
      <text class="label">昵称</text>
      <view class="value-container">
        <text class="value">{{nickname}}</text>
        <text class="arrow">></text>
      </view>
    </view>
  </view>

  <!-- 手机号码区域 -->
  <view class="info-section">
    <view class="info-row">
      <text class="label">手机号码</text>
      <view class="value-container">
        <text class="value">{{phoneNumber}}</text>
        <text class="arrow">></text>
      </view>
    </view>
  </view>

  <!-- 邀请码区域 -->
  <view class="info-section">
    <view class="info-row">
      <text class="label">邀请码</text>
      <view class="value-container">
        <text class="value">{{inviteCode}}</text>
      </view>
    </view>
  </view>

  <!-- 推广授权区域 -->
  <view class="info-section">
    <view class="info-row">
      <text class="label">推广授权</text>
      <view class="value-container">
        <text class="value authorized">{{authStatus}}</text>
      </view>
    </view>
  </view>

  <!-- 用户协议区域 -->
  <view class="info-section">
    <view class="info-row" bindtap="onUserAgreementTap">
      <text class="label">用户协议</text>
      <view class="value-container">
        <text class="arrow">></text>
      </view>
    </view>
  </view>

  <!-- 个人隐私协议区域 -->
  <view class="info-section">
    <view class="info-row" bindtap="onPrivacyAgreementTap">
      <text class="label">个人隐私协议</text>
      <view class="value-container">
        <text class="arrow">></text>
      </view>
    </view>
  </view>
</view>
