/* pages/profile/profile.wxss */

/* iPhone 16 尺寸适配 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 20rpx 0;
}

/* 头像区域样式 */
.avatar-section {
  background-color: #ffffff;
  margin: 0 24rpx 20rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.avatar-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.avatar-container {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b35, #f7931e, #ffeb3b);
}

/* 信息区域样式 */
.info-section {
  background-color: #ffffff;
  margin: 0 24rpx 20rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  position: relative;
}

.info-row:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 32rpx;
  right: 32rpx;
  height: 1rpx;
  background-color: #f0f0f0;
}

.label {
  font-size: 32rpx;
  color: #333333;
  font-weight: 400;
}

.value-container {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.value {
  font-size: 32rpx;
  color: #666666;
  margin-right: 16rpx;
}

.value.authorized {
  color: #ff4757;
  font-weight: 500;
}

.arrow {
  font-size: 32rpx;
  color: #cccccc;
  font-weight: 300;
}

/* 响应式设计 - iPhone 16 适配 */
@media screen and (min-width: 393px) {
  .container {
    max-width: 393px;
    margin: 0 auto;
  }
}

/* 微交互效果 */
.info-row:active {
  background-color: #f8f8f8;
  transform: scale(0.98);
  transition: all 0.1s ease;
}

.avatar-row:active {
  background-color: #f8f8f8;
  transform: scale(0.98);
  transition: all 0.1s ease;
}

/* 确保在不同设备上的适配 */
.info-section:last-child {
  margin-bottom: 40rpx;
}
